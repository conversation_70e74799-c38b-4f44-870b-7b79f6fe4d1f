import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './hooks/useAuthContext';
import { TenantProvider } from './hooks/useTenantContext';
import { LayoutProvider, useLayout } from './hooks/useLayoutContext';
import { TopBarProvider } from './hooks/useTopBarContext';
import ProtectedRoute from './components/auth/ProtectedRoute';
import Sidebar from './components/layout/Sidebar';
import HelpButton from './components/layout/HelpButton';
import Dashboard from './pages/Dashboard';
import DataPage from './pages/DataPage';
import Settings from './pages/Settings';
import SiteDashboard from './pages/SiteDashboard';
import WorkerDirectory from './pages/WorkerDirectory';
import WorkerProfile from './pages/WorkerProfile';
import CompanyWorkerManagement from './pages/CompanyWorkerManagement';
import TimeManagement from './pages/TimeManagement';
import CompanyReports from './pages/CompanyReports';
import CompanyEquipmentPage from './pages/CompanyEquipmentPage';
import CompanyTrainingPage from './pages/CompanyTrainingPage';
import TrainingPage from './pages/TrainingPage';
import TasksPage from './pages/TasksPage';
import TaskDetailPage from './pages/TaskDetailPage';
import DeviceDetailPage from './pages/DeviceDetailPage';
import {
  SiteEngineerDashboard,
  SiteEngineerPage,
  TasksPage as SiteEngineerTasksPage,
  PermitsPage as SiteEngineerPermitsPage,
  PermitDetailPage,
  WeatherPage as SiteEngineerWeatherPage,
  NotificationsPage as SiteEngineerNotificationsPage,
  AccountPage as SiteEngineerAccountPage,
  OvertimePage as SiteEngineerOvertimePage
} from './pages/site-engineer';
// import TaskRequestPage from './pages/TaskRequestPage';
import NewTaskPage from './pages/NewTaskPage';
import ReviewTaskPage from './pages/ReviewTaskPage';
import ApproveTaskPage from './pages/ApproveTaskPage';
import CloseTaskPage from './pages/CloseTaskPage';
import AllTasksPage from './pages/AllTasksPage';
import PermitsPage from './pages/PermitsPage';
// import MainPermitDetailPage from './pages/PermitDetailPage';
import InspectionsPage from './pages/InspectionsPage';
import SafetyPage from './pages/SafetyPage';
import EquipmentPage from './pages/EquipmentPage';
import PPEPage from './pages/PPEPage';
import WeatherPage from './pages/WeatherPage';
import DocumentsPage from './pages/DocumentsPage';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import ForgotPasswordPage from './pages/ForgotPasswordPage';
import ResetPasswordPage from './pages/ResetPasswordPage';
import AccountPage from './pages/AccountPage';
import PageNotFound from './pages/PageNotFound';
import ToolboxPage from './pages/ToolboxPage';
import NotificationsPage from './pages/NotificationsPage';
import StandaloneObservationPage from './pages/StandaloneObservationPage';
import CreateWorkerPage from './pages/CreateWorkerPage';
import UpdateWorkerPage from './pages/UpdateWorkerPage';
import WorkerFormDemoPage from './pages/WorkerFormDemoPage';
import RootRedirect from './components/auth/RootRedirect';
import NewSitePage from './pages/NewSitePage';
import InspectionFormPage from './pages/InspectionFormPage';
import InspectionFormDemoPage from './pages/InspectionFormDemoPage';
import PTWFormDemoPage from './pages/PTWFormDemoPage';
import ConfinedSpaceFormDemoPage from './pages/ConfinedSpaceFormDemoPage';
import ExcavationFormDemoPage from './pages/ExcavationFormDemoPage';
import WorkAtHeightFormDemoPage from './pages/WorkAtHeightFormDemoPage';
import HotWorkFormDemoPage from './pages/HotWorkFormDemoPage';
import InspectionFormsListPage from './pages/InspectionFormsListPage';
// import InspectionViewPage from './pages/InspectionViewPage';
import PTWFormPage from './pages/PTWFormPage';
import ConfinedSpaceFormPage from './pages/ConfinedSpaceFormPage';
import ExcavationFormPage from './pages/ExcavationFormPage';
import WorkAtHeightFormPage from './pages/WorkAtHeightFormPage';
import HotWorkFormPage from './pages/HotWorkFormPage';
import ExcavationPermitDisplayPage from './pages/permits/ExcavationPermitDisplayPage';
import ConfinedSpacePermitDisplayPage from './pages/permits/ConfinedSpacePermitDisplayPage';
import GeneralWorkPermitDisplayPage from './pages/permits/GeneralWorkPermitDisplayPage';
import HotWorkPermitDisplayPage from './pages/permits/HotWorkPermitDisplayPage';
import WorkAtHeightPermitDisplayPage from './pages/permits/WorkAtHeightPermitDisplayPage';
import ApprovePermitPage from './pages/permits/ApprovePermitPage';
import SiteDocumentsPage from './pages/SiteDocumentsPage';
import FormsPage from './pages/FormsPage';
import SiteInfoPage from './pages/SiteInfoPage';
import ToolboxFillPage from './pages/toolbox/ToolboxFillPage';
import ToolboxAttendancePage from './pages/toolbox/ToolboxAttendancePage';
import ToolboxSummarizePage from './pages/toolbox/ToolboxSummarizePage';
import NewTrainingForm from './components/training/NewTrainingForm';
import TrainingSessionSchedulingPage from './pages/TrainingSessionSchedulingPage';
import './index.css';

// Component to handle conditional sidebar rendering
const AppLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { showSidebar } = useLayout();

  return (
    <div className="bg-[#f3f2ee] min-h-screen font-[Inter]">
      {showSidebar && <Sidebar />}
      <main className="min-h-screen">
        {children}
      </main>
    </div>
  );
};


function App() {
  useEffect(() => {
    document.title = 'Workforce Management System';
  }, []);

  return (
    <AuthProvider>
      <TenantProvider>
        <Router>
          <LayoutProvider>
            <TopBarProvider>
              <Routes>
                {/* Root Route - Redirect based on authentication */}
                <Route path="/" element={<RootRedirect />} />

                {/* Public Routes */}
                <Route path="/login" element={<LoginPage />} />
                <Route path="/register" element={<RegisterPage />} />
                <Route path="/forgot-password" element={<ForgotPasswordPage />} />
                <Route path="/reset-password" element={<ResetPasswordPage />} />

                {/* Standalone External Routes */}
                <Route path="/sites/:siteId/observation" element={<StandaloneObservationPage />} />

                {/* Site Engineer Routes */}
                <Route path="/sites/:siteId/engineer" element={
                  <ProtectedRoute>
                    <SiteEngineerDashboard />
                  </ProtectedRoute>
                } />
                <Route path="/sites/:siteId/engineer/tasks" element={
                  <ProtectedRoute>
                    <SiteEngineerTasksPage />
                  </ProtectedRoute>
                } />
                <Route path="/sites/:siteId/engineer/permits" element={
                  <ProtectedRoute>
                    <SiteEngineerPermitsPage />
                  </ProtectedRoute>
                } />
                <Route path="/sites/:siteId/engineer/permits/:permitId" element={
                  <ProtectedRoute>
                    <PermitDetailPage />
                  </ProtectedRoute>
                } />
                <Route path="/sites/:siteId/engineer/weather" element={
                  <ProtectedRoute>
                    <SiteEngineerWeatherPage />
                  </ProtectedRoute>
                } />
                <Route path="/sites/:siteId/engineer/tasks/create" element={
                  <ProtectedRoute>
                    <SiteEngineerPage />
                  </ProtectedRoute>
                } />
                <Route path="/sites/:siteId/engineer/notifications" element={
                  <ProtectedRoute>
                    <SiteEngineerNotificationsPage />
                  </ProtectedRoute>
                } />
                <Route path="/sites/:siteId/engineer/account" element={
                  <ProtectedRoute>
                    <SiteEngineerAccountPage />
                  </ProtectedRoute>
                } />
                <Route path="/sites/:siteId/engineer/overtime" element={
                  <ProtectedRoute>
                    <SiteEngineerOvertimePage />
                  </ProtectedRoute>
                } />

                {/* Protected Routes */}
                <Route path="/*" element={
                  <ProtectedRoute>
                    <AppLayout>
                      <Routes>
                        <Route path="/dashboard" element={<Dashboard />} />
                        <Route path="/sites/new" element={<NewSitePage />} />
                        <Route path="/data" element={<DataPage />} />
                        <Route path="/devices/:deviceId" element={<DeviceDetailPage />} />
                        <Route path="/settings" element={<Settings />} />
                        <Route path="/account" element={<AccountPage />} />
                        <Route path="/notifications" element={<NotificationsPage />} />
                        <Route path="/documents" element={<DocumentsPage />} />
                        <Route path="/company-reports" element={<CompanyReports />} />
                        <Route path="/workers" element={<CompanyWorkerManagement />} />
                        <Route path="/company-equipment" element={<CompanyEquipmentPage />} />
                        <Route path="/company-training" element={<CompanyTrainingPage />} />
                        <Route path="/workers/create" element={<CreateWorkerPage />} />
                        <Route path="/demo/worker-form" element={<WorkerFormDemoPage />} />
                        <Route path="/demo/inspection-form" element={<InspectionFormDemoPage />} />
                        <Route path="/demo/ptw-form" element={<PTWFormDemoPage />} />
                        <Route path="/demo/confined-space-form" element={<ConfinedSpaceFormDemoPage />} />
                        <Route path="/demo/excavation-form" element={<ExcavationFormDemoPage />} />
                        <Route path="/demo/work-at-height-form" element={<WorkAtHeightFormDemoPage />} />
                        <Route path="/demo/hot-work-form" element={<HotWorkFormDemoPage />} />
                        {/* <Route path="/inspections/form" element={<InspectionFormsListPage />} />
                        <Route path="/inspections/form/:id" element={<InspectionFormPage />} />
                      <Route path="/inspections/view/:id" element={<InspectionViewPage />} />
                      <Route path="/inspections/demo" element={<InspectionFormDemoPage />} /> */}
                        <Route path="/ptw/form/:id" element={<PTWFormPage />} />
                        <Route path="/ptw-form" element={<PTWFormPage />} />
                        <Route path="/confined-space/form/:id" element={<ConfinedSpaceFormPage />} />
                        <Route path="/confined-space/form" element={<ConfinedSpaceFormPage />} />
                        <Route path="/excavation/form/:id" element={<ExcavationFormPage />} />
                        <Route path="/excavation/form" element={<ExcavationFormPage />} />
                        <Route path="/work-at-height/form/:id" element={<WorkAtHeightFormPage />} />
                        <Route path="/work-at-height/form" element={<WorkAtHeightFormPage />} />
                        <Route path="/hot-work/form/:id" element={<HotWorkFormPage />} />
                        <Route path="/hot-work/form" element={<HotWorkFormPage />} />
                        <Route path="/permits/excavation/:permitId" element={<ExcavationPermitDisplayPage />} />
                        <Route path="/permits/confined-space/:permitId" element={<ConfinedSpacePermitDisplayPage />} />
                        <Route path="/permits/general-work/:permitId" element={<GeneralWorkPermitDisplayPage />} />
                        <Route path="/permits/hot-work/:permitId" element={<HotWorkPermitDisplayPage />} />
                        <Route path="/permits/work-at-height/:permitId" element={<WorkAtHeightPermitDisplayPage />} />
                        <Route path="/sites/:siteId/dashboard" element={<SiteDashboard />} />
                        <Route path="/sites/:siteId/workers" element={<WorkerDirectory />} />
                        <Route path="/sites/:siteId/workers/new" element={<CreateWorkerPage />} />
                        <Route path="/sites/:siteId/workers/create" element={<CreateWorkerPage />} />
                        <Route path="/sites/:siteId/workers/:workerId/edit" element={<UpdateWorkerPage />} />
                        <Route path="/sites/:siteId/workers/:workerId" element={<WorkerProfile />} />
                        <Route path="/sites/:siteId/time" element={<TimeManagement />} />
                        <Route path="/sites/:siteId/training" element={<TrainingPage />} />
                        <Route path="/sites/:siteId/training/new" element={<NewTrainingForm />} />
                        <Route path="/sites/:siteId/training/schedule" element={<TrainingSessionSchedulingPage />} />
                        <Route path="/sites/:siteId/tasks" element={<TasksPage />} />
                        <Route path="/sites/:siteId/tasks/new" element={<NewTaskPage />} />
                        <Route path="/sites/:siteId/tasks/review/:id" element={<ReviewTaskPage />} />
                        <Route path="/sites/:siteId/tasks/approve" element={<ApproveTaskPage />} />
                        <Route path="/sites/:siteId/tasks/close" element={<CloseTaskPage />} />
                        <Route path="/sites/:siteId/tasks/all" element={<AllTasksPage />} />
                        {/* <Route path="/sites/:siteId/tasks/request/:taskId" element={<TaskRequestPage />} /> */}
                        <Route path="/sites/:siteId/tasks/:taskId" element={<TaskDetailPage />} />
                        {/* <Route path="/sites/:siteId/tasks/*" element={<TasksPage />} /> */}
                        <Route path="/sites/:siteId/permits" element={<PermitsPage />} />
                        <Route path="/sites/:siteId/permits/approve" element={<ApprovePermitPage />} />
                        <Route path="/sites/:siteId/permits/excavation/:permitId" element={<ExcavationPermitDisplayPage />} />
                        <Route path="/sites/:siteId/permits/confined-space/:permitId" element={<ConfinedSpacePermitDisplayPage />} />
                        <Route path="/sites/:siteId/permits/general-work/:permitId" element={<GeneralWorkPermitDisplayPage />} />
                        <Route path="/sites/:siteId/permits/hot-work/:permitId" element={<HotWorkPermitDisplayPage />} />
                        <Route path="/sites/:siteId/permits/work-at-height/:permitId" element={<WorkAtHeightPermitDisplayPage />} />
                        <Route path="/sites/:siteId/inspections" element={<InspectionsPage />} />
                        <Route path="/sites/:siteId/inspections/form" element={<InspectionFormsListPage />} />
                        <Route path="/sites/:siteId/inspections/form/:id" element={<InspectionFormPage />} />
                        <Route path="/sites/:siteId/ptw-form" element={<PTWFormPage />} />
                        <Route path="/sites/:siteId/ptw-form/:id" element={<PTWFormPage />} />
                        <Route path="/sites/:siteId/confined-space/form/:id" element={<ConfinedSpaceFormPage />} />
                        <Route path="/sites/:siteId/confined-space/form" element={<ConfinedSpaceFormPage />} />
                        <Route path="/sites/:siteId/excavation/form/:id" element={<ExcavationFormPage />} />
                        <Route path="/sites/:siteId/excavation/form" element={<ExcavationFormPage />} />
                        <Route path="/sites/:siteId/work-at-height/form/:id" element={<WorkAtHeightFormPage />} />
                        <Route path="/sites/:siteId/work-at-height/form" element={<WorkAtHeightFormPage />} />
                        <Route path="/sites/:siteId/hot-work/form/:id" element={<HotWorkFormPage />} />
                        <Route path="/sites/:siteId/hot-work/form" element={<HotWorkFormPage />} />
                        <Route path="/sites/:siteId/safety" element={<SafetyPage />} />
                        <Route path="/sites/:siteId/equipment" element={<EquipmentPage />} />
                        <Route path="/sites/:siteId/ppe" element={<PPEPage />} />
                        <Route path="/sites/:siteId/weather" element={<WeatherPage />} />
                        <Route path="/sites/:siteId/documents" element={<SiteDocumentsPage />} />
                        <Route path="/sites/:siteId/forms" element={<FormsPage />} />
                        <Route path="/sites/:siteId/toolbox" element={<ToolboxPage />} />
                        <Route path="/sites/:siteId/toolbox/fill" element={<ToolboxFillPage />} />
                        <Route path="/sites/:siteId/toolbox/attendance" element={<ToolboxAttendancePage />} />
                        <Route path="/sites/:siteId/toolbox/summarize" element={<ToolboxSummarizePage />} />
                        {/* <Route path="/toolbox/fill" element={<ToolboxFillPage />} />
                      <Route path="/toolbox/attendance" element={<ToolboxAttendancePage />} />
                      <Route path="/toolbox/summarize" element={<ToolboxSummarizePage />} /> */}
                        <Route path="/sites/:siteId/info" element={<SiteInfoPage />} />
                        <Route path="*" element={<PageNotFound />} />
                      </Routes>
                      <HelpButton />
                    </AppLayout>
                  </ProtectedRoute>
                } />
              </Routes>
            </TopBarProvider>
          </LayoutProvider>
        </Router>
      </TenantProvider>
    </AuthProvider>
  );
}

export default App;
